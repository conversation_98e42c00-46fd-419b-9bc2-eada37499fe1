#include "BrushRenderer.h"
#include <QLineF>
#include <QtMath>
#include <QDebug>

BrushRenderer::Brush<PERSON>enderer(QObject* parent)
    : QObject(parent)
    , m_lastPointTime(0)
    , m_speedThreshold(20.0)
    , m_minTaperPoints(3)
    , m_maxTaperPoints(10)
    , m_catchupDelay(100)
{
}

void BrushRenderer::startStroke(const QPointF& startPoint, qreal baseWidth)
{
    m_currentStroke.clear();
    m_currentStroke.setBaseWidth(baseWidth);
    
    // 添加起始点
    m_currentStroke.points.append(startPoint);
    m_currentStroke.originalWidths.append(baseWidth);
    m_currentStroke.currentWidths.append(baseWidth);
    
    m_lastPointTime = QDateTime::currentMSecsSinceEpoch();
    
    qDebug() << "[BrushRenderer] Started stroke with base width:" << baseWidth;
}

void BrushRenderer::addPoint(const QPointF& point)
{
    if (m_currentStroke.isEmpty()) {
        qWarning() << "[Brush<PERSON><PERSON><PERSON>] Cannot add point to empty stroke";
        return;
    }
    
    // 计算速度
    qreal speed = 0;
    if (m_currentStroke.pointCount() > 0) {
        speed = calculateSpeed(m_currentStroke.points.last(), point);
    }
    
    // 添加点
    m_currentStroke.points.append(point);
    m_currentStroke.originalWidths.append(m_currentStroke.baseWidth);
    m_currentStroke.currentWidths.append(m_currentStroke.baseWidth);
    
    // 更新笔锋点数
    m_currentStroke.taperPointCount = calculateTaperPoints(speed, m_currentStroke.baseWidth);
    
    // 更新笔锋宽度
    updateTaperWidths();
    
    // 重置100ms定时器
    resetCatchupTimer();
    
    emit strokeUpdated();
    
    qDebug() << "[BrushRenderer] Added point, speed:" << speed 
             << "taper points:" << m_currentStroke.taperPointCount;
}

void BrushRenderer::finishStroke()
{
    if (m_currentStroke.isEmpty()) {
        return;
    }
    
    // 停止定时器
    if (m_currentStroke.catchupTimer) {
        m_currentStroke.catchupTimer->stop();
    }
    
    // 立即追平所有点
    catchupTaper();
    
    emit strokeFinished();
    
    qDebug() << "[BrushRenderer] Finished stroke with" << m_currentStroke.pointCount() << "points";
}

void BrushRenderer::cancelStroke()
{
    m_currentStroke.clear();
    qDebug() << "[BrushRenderer] Cancelled stroke";
}

qreal BrushRenderer::calculateSpeed(const QPointF& lastPoint, const QPointF& currentPoint)
{
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    qint64 deltaTime = currentTime - m_lastPointTime;
    m_lastPointTime = currentTime;
    
    if (deltaTime <= 0) {
        return 0.0;
    }
    
    qreal distance = QLineF(lastPoint, currentPoint).length();
    return distance; // 简化为距离，代表速度
}

int BrushRenderer::calculateTaperPoints(qreal speed, qreal baseWidth)
{
    int maxPoints = qMax(m_maxTaperPoints, (int)(baseWidth));
    
    qreal speedRatio = qMin(speed / m_speedThreshold, 3.0); // 最大3倍
    
    int taperPoints = m_minTaperPoints + (int)((maxPoints - m_minTaperPoints) * speedRatio);
    
    return qBound(m_minTaperPoints, taperPoints, maxPoints);
}

void BrushRenderer::updateTaperWidths()
{
    int pointCount = m_currentStroke.pointCount();
    int taperCount = m_currentStroke.taperPointCount;
    
    // 重置所有宽度为原始宽度
    for (int i = 0; i < pointCount; ++i) {
        m_currentStroke.currentWidths[i] = m_currentStroke.baseWidth;
    }
    
    // 只对最后taperCount个点进行笔锋处理
    if (taperCount > 0 && pointCount > 1) {
        int startIndex = qMax(0, pointCount - taperCount);
        for (int i = startIndex; i < pointCount; ++i) {
            qreal t = (qreal)(i - startIndex) / (taperCount - 1);
            qreal widthRatio = 1.0 - t * 0.8; // 从100%递减到20%
            m_currentStroke.currentWidths[i] = m_currentStroke.baseWidth * widthRatio;
        }
    }
}

void BrushRenderer::resetCatchupTimer()
{
    if (!m_currentStroke.catchupTimer) {
        m_currentStroke.catchupTimer = new QTimer(this);
        connect(m_currentStroke.catchupTimer, &QTimer::timeout, this, &BrushRenderer::catchupTaper);
        m_currentStroke.catchupTimer->setSingleShot(true);
    }
    
    m_currentStroke.catchupTimer->stop();
    m_currentStroke.catchupTimer->start(m_catchupDelay);
}

void BrushRenderer::catchupTaper()
{
    // 追平：将所有点的宽度恢复为原始宽度
    for (int i = 0; i < m_currentStroke.currentWidths.size(); ++i) {
        m_currentStroke.currentWidths[i] = m_currentStroke.baseWidth;
    }
    m_currentStroke.taperPointCount = 0;

    emit strokeUpdated();

    qDebug() << "[BrushRenderer] Caught up taper";
}

void BrushRenderer::renderVariableWidthPath(QPainter* painter, const BrushStroke& stroke)
{
    if (stroke.pointCount() < 2 || !painter) {
        return;
    }

    // 保存原始画笔状态
    QPen originalPen = painter->pen();
    QPen variablePen = originalPen;

    // 设置画笔属性以获得更好的效果
    variablePen.setCapStyle(Qt::RoundCap);
    variablePen.setJoinStyle(Qt::RoundJoin);

    // 逐段绘制，每段使用对应的宽度
    for (int i = 0; i < stroke.pointCount() - 1; ++i) {
        // 使用当前点的宽度
        qreal currentWidth = stroke.currentWidths[i];
        variablePen.setWidthF(currentWidth);
        painter->setPen(variablePen);

        // 绘制线段
        painter->drawLine(stroke.points[i], stroke.points[i + 1]);
    }

    // 恢复原始画笔
    painter->setPen(originalPen);
}
