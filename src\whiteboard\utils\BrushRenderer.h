#ifndef BRUSHRENDERER_H
#define BRUSHRENDERER_H

#include <QObject>
#include <QPointF>
#include <QPainter>
#include <QDateTime>
#include "BrushStroke.h"

/**
 * @brief 笔锋渲染器 - 处理基于速度的动态笔锋效果
 * 
 * 核心功能：
 * 1. 根据绘制速度动态计算笔锋点数
 * 2. 管理100ms追平定时器机制
 * 3. 提供变宽度路径渲染
 * 4. 处理笔锋宽度的实时更新
 */
class BrushRenderer : public QObject
{
    Q_OBJECT

public:
    explicit BrushRenderer(QObject* parent = nullptr);
    ~BrushRenderer() = default;

    // 笔锋管理
    void startStroke(const QPointF& startPoint, qreal baseWidth);
    void addPoint(const QPointF& point);
    void finishStroke();
    void cancelStroke();

    // 获取当前笔锋数据
    const BrushStroke& getCurrentStroke() const { return m_currentStroke; }
    bool hasActiveStroke() const { return !m_currentStroke.isEmpty(); }

    // 渲染方法
    static void renderVariableWidthPath(QPainter* painter, const BrushStroke& stroke);

    // 配置参数
    void setSpeedThreshold(qreal threshold) { m_speedThreshold = threshold; }
    void setMinTaperPoints(int points) { m_minTaperPoints = points; }
    void setMaxTaperPoints(int points) { m_maxTaperPoints = points; }
    void setCatchupDelay(int ms) { m_catchupDelay = ms; }

signals:
    void strokeUpdated();
    void strokeFinished();

private slots:
    void catchupTaper();

private:
    // 核心算法
    qreal calculateSpeed(const QPointF& lastPoint, const QPointF& currentPoint);
    int calculateTaperPoints(qreal speed, qreal baseWidth);
    void updateTaperWidths();
    void resetCatchupTimer();

private:
    BrushStroke m_currentStroke;
    qint64 m_lastPointTime;

    // 配置参数
    qreal m_speedThreshold;     // 速度阈值
    int m_minTaperPoints;       // 最小笔锋点数
    int m_maxTaperPoints;       // 最大笔锋点数
    int m_catchupDelay;         // 追平延迟(ms)
};

#endif // BRUSHRENDERER_H
