#ifndef BRUSHSTROKE_H
#define BRUSHSTROKE_H

#include <QVector>
#include <QPointF>
#include <QTimer>
#include <QObject>

/**
 * @brief 笔锋数据结构 - 存储绘制路径和宽度信息
 * 
 * 核心功能：
 * 1. 存储路径点和对应的宽度信息
 * 2. 管理笔锋效果的点数和追平定时器
 * 3. 支持基于速度的动态笔锋计算
 */
struct BrushStroke {
    QVector<QPointF> points;           // 所有路径点
    QVector<qreal> originalWidths;     // 原始宽度(都相同)
    QVector<qreal> currentWidths;      // 当前实际宽度
    int taperPointCount;               // 当前需要笔锋处理的点数
    QTimer* catchupTimer;              // 100ms追平定时器
    qreal baseWidth;                   // 基础线宽
    
    BrushStroke() 
        : taperPointCount(0)
        , catchupTimer(nullptr)
        , baseWidth(2.0)
    {
    }
    
    ~BrushStroke() {
        if (catchupTimer) {
            catchupTimer->deleteLater();
            catchupTimer = nullptr;
        }
    }
    
    // 清空所有数据
    void clear() {
        points.clear();
        originalWidths.clear();
        currentWidths.clear();
        taperPointCount = 0;
        if (catchupTimer) {
            catchupTimer->stop();
        }
    }
    
    // 获取点数量
    int pointCount() const {
        return points.size();
    }
    
    // 检查是否为空
    bool isEmpty() const {
        return points.isEmpty();
    }
    
    // 设置基础宽度
    void setBaseWidth(qreal width) {
        baseWidth = width;
    }
};

#endif // BRUSHSTROKE_H
